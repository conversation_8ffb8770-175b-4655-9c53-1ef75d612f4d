#!/bin/bash

echo "=== MoZo.Live Startup Script ==="

# Wait for container to be fully ready
sleep 5

# Get container name
CONTAINER_NAME=$(hostname)
echo "Container: $CONTAINER_NAME"

# Fix database configuration
echo "Fixing database configuration..."
sed -i "s/'hostname'     => 'WRITE_hostname'/'hostname'     => '**********'/g" /var/www/html/app/Config/Database.php
sed -i "s/'username'     => 'WRITE_db_username'/'username'     => 'mysql'/g" /var/www/html/app/Config/Database.php
sed -i "s/'password'     => 'WRITE_db_password'/'password'     => 'GJCxnXUvJdSPrt8a'/g" /var/www/html/app/Config/Database.php
sed -i "s/'database'     => 'WRITE_database_name'/'database'     => 'default'/g" /var/www/html/app/Config/Database.php
sed -i "s/'DBPrefix'     => 'WRITE_dbprefix'/'DBPrefix'     => 'lon48_'/g" /var/www/html/app/Config/Database.php

# Fix any env() function calls
sed -i "s/env('DB_HOSTNAME', '[^']*')/'**********'/g" /var/www/html/app/Config/Database.php
sed -i "s/env('DB_USERNAME', '[^']*')/'mysql'/g" /var/www/html/app/Config/Database.php
sed -i "s/env('DB_PASSWORD', '[^']*')/'GJCxnXUvJdSPrt8a'/g" /var/www/html/app/Config/Database.php
sed -i "s/env('DB_DATABASE', '[^']*')/'default'/g" /var/www/html/app/Config/Database.php

# Fix base URL
echo "Setting base URL..."
sed -i 's/public string $baseURL = "";/public string $baseURL = "https:\/\/mozo.live\/";/' /var/www/html/app/Config/App.php

# Fix session handler
echo "Setting session handler..."
sed -i "s/public string \$driver = 'CodeIgniter\\\\Session\\\\Handlers\\\\DatabaseHandler';/public string \$driver = 'CodeIgniter\\\\Session\\\\Handlers\\\\FileHandler';/" /var/www/html/app/Config/Session.php

# Fix installation state
echo "Setting installation state..."
sed -i 's/$app_state = "pre_installation";/$app_state = "installed";/' /var/www/html/index.php

# Remove install directory if exists
if [ -d "/var/www/html/install" ]; then
    echo "Removing install directory..."
    rm -rf /var/www/html/install
fi

# Set proper permissions
echo "Setting permissions..."
chown -R www-data:www-data /var/www/html/writable
chmod -R 777 /var/www/html/writable

echo "=== Startup fixes completed ==="

# Start Apache
exec apache2-foreground
